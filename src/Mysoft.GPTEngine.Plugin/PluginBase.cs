using AutoMapper;
using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.ML.Tokenizers;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Memory;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Extensions;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Plugin.Resources;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Mysoft.GPTEngine.SemanticKernel.Core.Extensions;
using Mysoft.GPTEngine.SemanticKernel.Core.Kimi;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Encodings.Web;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using KernelBuilderExtensions = Mysoft.GPTEngine.SemanticKernel.Core.Extensions.KernelBuilderExtensions;

namespace Mysoft.GPTEngine.Plugin
{
#pragma warning disable CS8601 // 引用类型赋值可能为 null。
    public class PluginBase
    {
        public readonly IHttpContextAccessor _httpContextAccessor;
        public readonly IMapper _mapper;
        public readonly MemoryBuilder _memoryBuilder;
        public readonly Kernel _kernel;
        public static readonly Tokenizer s_tokenizer = Tokenizer.CreateTiktokenForModel("gpt-4");
        public readonly IMysoftContextFactory _mysoftContextFactory;
        #region 格式化提示词
        public readonly string formatPrompt = @"
## 限制
- 严格以 JSON 格式返回答案。
- 严格确保以下回答为有效的 JSON 格式。
- 输出应按照符合 JSON 模式的格式进行格式化，且不得添加注释。

## Output 输出要求
- 认真思考用户问题，确保答案逻辑合理、有意义。
- 解释要简洁明了，易于理解，避免冗长。
- 严格以 JSON 格式返回答案。
- 确保答案为有效的 JSON 格式。
- 输出应按照符合 JSON 模式的格式进行格式化。以下是输出 schema：";
        #endregion
        public PluginBase(Kernel kernel, MemoryBuilder memoryBuilder, IHttpContextAccessor httpContextAccessor, IMapper mapper, IMysoftContextFactory mysoftContextFactory)
        {
            if (kernel == null || kernel?.Services == null) throw new ArgumentNullException(nameof(kernel));

            _kernel = kernel;
            _memoryBuilder = memoryBuilder;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _mysoftContextFactory = mysoftContextFactory;
        }

        public async Task<string> ChatCompletion(string systemPromptName, string input, KernelArguments? arguments = null, bool textEvent = false,
            string? serviceId = null,
            string? executionSetting = null,
            CancellationToken cancellationToken = default)
        {
            arguments = arguments ?? await (await GetChatRunDto()).GetNodeInputArgument();
            var promptTemplateText = EmbeddedResource.Read(systemPromptName);
            var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            Console.WriteLine("大模型调用：" + systemPrompy.Content);
            var chatHistory = await CreateChatHistory(systemMessage: systemPrompy.Content, history: 3);
            if (string.IsNullOrWhiteSpace(input) == false)
            {
                chatHistory.AddUserMessage(input);
            }
            Console.WriteLine("input：" + input);
            return await ChatCompletion(chatHistory: chatHistory, textEvent: textEvent, serviceId: serviceId, executionSetting: executionSetting, cancellationToken: cancellationToken);
        }

        public async Task<string> ChatCompletionByMemories(string systemPromptName, string input, KernelArguments? arguments = null, bool textEvent = false,
            string? serviceId = null,
            string? executionSetting = null,
            CancellationToken cancellationToken = default)
        {
            arguments = arguments ?? await (await GetChatRunDto()).GetNodeInputArgument();
            var promptTemplateText = EmbeddedResource.Read(systemPromptName);
            var systemPrompy = await TemplateRenderAsync(promptTemplateText: promptTemplateText, arguments: arguments);
            Console.WriteLine("大模型调用：" + systemPrompy.Content);
            var chatHistory = await CreateChatHistoryWithMemories(systemMessage: systemPrompy.Content, true);
            if (string.IsNullOrWhiteSpace(input) == false)
            {
                chatHistory.AddUserMessage(input);
            }
            Console.WriteLine("input：" + input);
            return await ChatCompletion(chatHistory: chatHistory, textEvent: textEvent, serviceId: serviceId, executionSetting: executionSetting, cancellationToken: cancellationToken);
        }

        public async Task<string> ChatCompletion(ChatHistory chatHistory, bool textEvent = false, string? serviceId = null,
            string? executionSetting = null,
            CancellationToken cancellationToken = default)
        {
            var chatRunDto = await GetChatRunDto();
            var modelInstance = chatRunDto.ModelInstance;
            if (_kernel == null) throw new ArgumentNullException(nameof(_kernel));

            var chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>();

            if (string.IsNullOrWhiteSpace(serviceId) == false)
            {
                Console.WriteLine("模型serviceId：" + serviceId);
                chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(serviceId);
            }
            else if (modelInstance != null && modelInstance.IsDefault == false)
            {
                Console.WriteLine("模型InstanceCode：" + modelInstance.InstanceCode);
                chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(modelInstance.InstanceCode);
            }
            PromptExecutionSettings? executionSettings = null;
            if (string.IsNullOrWhiteSpace(executionSetting) == false)
            {
                executionSettings = KernelBuilderExtensions.DeserializePromptExecutionSettings(executionSetting);
            }
            var content = string.Empty;
            var competionTokes = 0;
            await foreach (var item in chatCompletionService.GetStreamingChatMessageContentsAsync(chatHistory: chatHistory, executionSettings: executionSettings, cancellationToken: cancellationToken))
            {
                if (item.Metadata != null && item.Metadata.TryGetValue("UsageCompletionTokens", out var usageCompletionTokens))
                {
                    competionTokes = int.Parse(usageCompletionTokens?.ToString() ?? competionTokes.ToString());
                }
                else
                {
                    competionTokes += 1;
                }
                if (string.IsNullOrEmpty(item.Content) == false)
                {
                    content += item.Content;
                    if (textEvent)
                    {
                        await TextEvent(item.Content, chatRunDto.IsStream);
                    }
                }
            }

            if (chatRunDto != null)
            {
                await chatRunDto.AddPromptNodeLog(chatHistory.Sum(x => s_tokenizer.CountTokens(x.Content ?? string.Empty)), competionTokes);
            }
            return await Task.FromResult(content);
        }

        public async Task<string> ParameterDecryption(string parameter)
        {
            return await Task.FromResult(Encoding.UTF8.GetString(Convert.FromBase64String(parameter)));
        }
        public async Task AddMessage(string role, string content)
        {
            var userContext = _mysoftContextFactory.GetMysoftContext().UserContext;
            var chatRunDto = (await GetChatRunDto().ConfigureAwait(false)) ?? throw new ArgumentNullException(nameof(ChatRunDto));

            var index = chatRunDto.ChatMessages.Count;

            // 判断是否为工具相关消息，如果是则隐藏
            var isToolMessage = role == ChatRoleConstant.Tool ||
                               (role == ChatRoleConstant.Assistant && (content?.StartsWith("[TOOL_CALL]") == true));

            var dto = new ChatMessageDto
            {
                ChatGUID = chatRunDto.Chat?.ChatGUID ?? Guid.Empty,
                NodeGUID = chatRunDto.Chat?.CurrentNodeGUID ?? Guid.Empty,
                Role = role,
                Content = content,
                Index = index,
                BatchGUID = chatRunDto.BatchGuid,
                IsHidden = isToolMessage ? 1 : 0 // 工具相关消息隐藏，其他消息不隐藏
            };

            // 如果是助手消息，将内容也设置到Answer字段
            if (role == ChatRoleConstant.Assistant)
            {
                dto.Answer = content;
            }

            chatRunDto.ChatMessages.Add(dto);

            await Task.CompletedTask;
        }
        public async Task<ChatHistory> CreateChatHistory(PromptDto prompt, KernelArguments? arguments)
        {
            ChatHistory chatHistory;
            if (prompt.MessageContents == null || prompt.MessageContents.Count == 0)
            {
                var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);

                chatHistory = await CreateChatHistory();
                await AddPromptChatHistory(chatHistory);
                chatHistory.AddUserMessage(promptTemplate.Content);
            }
            else
            {
                var promptTemplate = await TemplateRenderAsync(promptTemplateText: prompt.PromptTemplate, arguments: arguments);

                chatHistory = await CreateChatHistory(promptTemplate.Content);
                await AddPromptChatHistory(chatHistory);
                foreach (var item in prompt.MessageContents)
                {
                    promptTemplate = await TemplateRenderAsync(promptTemplateText: item.Content, arguments: arguments);
                    if (item.Role == AuthorRole.User.Label)
                    {
                        chatHistory.AddUserMessage(promptTemplate.Content);
                    }
                    else if (item.Role == AuthorRole.Assistant.Label)
                    {
                        chatHistory.AddAssistantMessage(promptTemplate.Content);
                    }
                }
            }
            Console.WriteLine("提示词节点ChatHistory的数据：" + JsonConvert.SerializeObject(chatHistory));
            return await Task.FromResult(chatHistory);
        }


        public async Task<ChatHistory> CreateChatHistory(string? systemMessage = null, int history = 0)
        {
            var chatHistory = systemMessage == null ? new ChatHistory() : new ChatHistory(systemMessage);
            if (history == 0) return await Task.FromResult(chatHistory);


            var chatRunDto = await GetChatRunDto();
            if (chatRunDto?.ChatMessages != null && chatRunDto.ChatMessages.Count > 0)
            {
                var nodeGUID = chatRunDto.Chat.CurrentNodeGUID;
                var chatMessage = chatRunDto.ChatMessages.Where(x => x.NodeGUID == nodeGUID).OrderByDescending(x => x.Index).Take(2 * history).OrderBy(x => x.Index).ToList();
                foreach (var message in chatMessage)
                {
                    switch (message.Role)
                    {
                        case ChatRoleConstant.User:
                            chatHistory.AddUserMessage(message.Content); break;
                        case ChatRoleConstant.Assistant:
                            chatHistory.AddAssistantMessage(message.Content); break;
                        default:
                            break;
                    }
                }
            }

            return await Task.FromResult(chatHistory);
        }

        /**
         * 会话记录带会话记忆
         */
        public async Task<ChatHistory> CreateChatHistoryWithMemories(string? systemMessage = null, bool memories = true)
        {
            var chatHistory = systemMessage == null ? new ChatHistory() : new ChatHistory(systemMessage);
            if (memories == false) return await Task.FromResult(chatHistory);
            var addPromptChatHistory = await AddPromptChatHistory(chatHistory);
            return await Task.FromResult(chatHistory);
        }


        public async Task<ChatHistory> AddPromptChatHistory(ChatHistory chatHistory)
        {
            var chatRunDto = await GetChatRunDto();
            if (chatRunDto.Nodes.Count == 0) return await Task.FromResult(chatHistory);
            var currentNode = await chatRunDto.GetFlowNode();
            var memories = currentNode.Config.Memories;
            var nodeGUID = chatRunDto.Chat.CurrentNodeGUID;
            if (memories == 0)
            {
                return await Task.FromResult(chatHistory);
            }
            var nodeLogs = chatRunDto.NodeLogs.Where(x => x.NodeGUID == nodeGUID && x.BatchGUID.ToString() != "00000000-0000-0000-0000-000000000000").OrderByDescending(x => x.Index).Take(memories).OrderBy(x => x.Index).ToList();
            foreach (var nodeLog in nodeLogs)
            {

                chatHistory.AddUserMessage(JsonUtility.RemoveInputHistoryProperty(nodeLog.Inputs));
                var nodeLogOutput = JsonUtility.IsContainsOutput(nodeLog.Outputs)
                    ? JsonUtility.OutPutValue(nodeLog.Outputs)
                    : nodeLog.Outputs;
                chatHistory.AddAssistantMessage(nodeLogOutput);
            }
            return await Task.FromResult(chatHistory);
        }


        public async Task<ChatRunDto> GetChatRunDto()
        {
            var chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto));
            return await Task.FromResult(chatRunDto);
        }

        public async Task NodeArgumentCheck(ChatRunDto chatRun)
        {
            var inputs = await chatRun.GetFlowNodeInputs();
            var outputs = await chatRun.GetFlowNodeOutputs();
            var node = await chatRun.GetFlowNode();
            foreach (var item in inputs)
            {
                string fieldInfo = string.IsNullOrEmpty(item.Name) ? item.Code : item.Code;
                if (string.IsNullOrEmpty(item.Code))
                {
                    throw new NoRequiredArgumentException($"节点【{node.Name}】输入参数配置缺失");
                }
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{node.Name}】参数【{fieldInfo}】没有设置");
                }
                //if (item.Required && string.IsNullOrEmpty(item.LiteralValue))
                //{
                //    throw new NoRequiredArgumentException($"节点【{node.Name}】参数【{fieldInfo}】没有传值");
                //}
            }
            validateOutputs(outputs,node.Name);
        }


        public void validateOutputs(List<ParamDto> paramDtos,string nodeName)
        {
            foreach (var item in paramDtos)
            {
                string fieldInfo = string.IsNullOrEmpty(item.Name) ? item.Code : item.Code;
                if (item == null)
                {
                    return;
                }
                if (string.IsNullOrEmpty(item.Code))
                {
                    throw new NoRequiredArgumentException($"节点【{nodeName}】输出参数配置缺失");
                }
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{nodeName}】参数【{fieldInfo}】没有设置");
                }
                if(item.Schema != null && item.Schema.Count > 0)
                {
                    validateOutputs(item.Schema, nodeName);
                }
            }
        }

        public async Task FileArgumentCheck(FlowNode flowNode)
        {
            foreach (var item in flowNode.Config.Files)
            {
                if (item.Required && (item.Value == null || string.IsNullOrEmpty(item.Value.Content)))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】字段【{item.Name ?? item.Code}】没有设置");
                }
                if (item.Required && string.IsNullOrEmpty(item.LiteralValue))
                {
                    throw new NoRequiredArgumentException($"节点【{flowNode.Name}】未上传文件");
                }
            }
            if(flowNode.Config.Outputs.Count == 0)
            {
                throw new NoRequiredArgumentException($"节点【{flowNode.Name}】输出参数不能为空");
            }
            await Task.CompletedTask;
        }

        public async Task<T> GetKeywordValue<T>(string key)
        {
            return await Task.FromResult(_httpContextAccessor.GetArgumentValue<T>(string.Format(KernelArgumentsConstant.Keyword, key)));
        }

        public async Task<TemplateDto> TemplateRenderAsync(string promptTemplateText, KernelArguments? arguments, Kernel? kernel = null)
        {
            var promptTemplateFactory = new KernelPromptTemplateFactory();

            var promptTemplateConfig = new PromptTemplateConfig(promptTemplateText)
            {
                //AllowUnsafeContent = true
            };
            var promptTemplate = promptTemplateFactory.Create(promptTemplateConfig);

            if (_kernel == null) throw new ArgumentNullException(nameof(_kernel));

            var template = new TemplateDto
            {
                Content = await promptTemplate.RenderAsync(_kernel, arguments),
                Inputs = promptTemplateConfig.InputVariables.Select(x => new ParamDto
                {
                    Code = x.Name,
                    LiteralValue = arguments?.GetValueOrDefault(x.Name)?.ToString()
                }).ToList()
            };

            template.Content = HttpUtility.HtmlDecode(template.Content);

            return await Task.FromResult(template);
        }

        public async Task AddFirstOutput(object result)
        {
            var chatRunDto = await GetChatRunDto().ConfigureAwait(false);
            var outputs = await chatRunDto.GetFlowNodeOutputs().ConfigureAwait(false);
            if (outputs == null || outputs.Count == 0) return;

            if (result is string resultString)
            {
                outputs.First().LiteralValue = resultString;
            }

            await Task.CompletedTask;
        }

        public Dictionary<string, object> JsonCompile(string jsons)
        {
            Console.WriteLine("大模型返回：" + jsons);
            // 截取从 startIndex 到 endIndex 的字符串
            jsons = JsonValidateHelper.ExtractJson(jsons);
            if (string.IsNullOrWhiteSpace(jsons) || !JsonValidateHelper.IsValidJson(jsons))
            {
                return new Dictionary<string, object>();
            }
            Dictionary<string, object> dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsons);
            return dictionary;
        }

        public void TraverseParamDtos(List<ParamDto> paramDtos, ref string str, string code = "Output")
        {
            str += $" {code}:" + "{ ";
            foreach (var paramDto in paramDtos)
            {
                if (!String.Equals(paramDto.Type, "array<object>", StringComparison.OrdinalIgnoreCase))
                {
                    str += $"\"{paramDto.Code}\": {paramDto.Type} //{paramDto.Name} {paramDto.Description} ";
                }
                else
                {
                    str += $"\"{paramDto.Code}\": List[{paramDto.Code}] //{paramDto.Name} {paramDto.Description} ";
                }
            }
            str += "}";
            foreach (var paramDto in paramDtos)
            {
                // 如果 Schema 不为空且包含元素，则递归调用此方法
                if (paramDto.Schema != null && paramDto.Schema.Count > 0)
                {
                    TraverseParamDtos(paramDto.Schema, ref str, paramDto.Code);
                }
            }
        }

        #region SSE Event
        public async Task TextEvent(string content, bool isStream)
        {
            if (isStream == false || string.IsNullOrWhiteSpace(content)) return;

            content = content.Replace("\n", "|n");

            // Console.WriteLine("----PluginBase.TextEvent:isStream:{0},content:{1}", isStream, content);

            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.TextEvent, content));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await Task.Delay(MysoftConstant.ChatDelay);
            // await AddMessage(AuthorRole.Tool.Label, content);
        }
        public async Task ErrorEvent(string message, bool isStream)
        {
            if (isStream == false || string.IsNullOrWhiteSpace(message)) return;

            Console.WriteLine("----PluginBase.ErrorEvent:isStream:{0},message:{1}", isStream, message);
            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ErrorEvent, message));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            await AddMessage(AuthorRole.Tool.Label, message);
        }
        public async Task ReplaceEvent(object content, bool isStream)
        {
            if (isStream == false || content == null) return;

            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(content, setting);

            Console.WriteLine("----PluginBase.ReplaceEvent:isStream:{0},content:{1}", isStream, content);

            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.ReplaceEvent, data));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.ReplaceEvent, data));
        }
        public async Task DataEvent(object content, bool isStream)
        {
            if (isStream == false || content == null) return;

            var setting = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
            };
            var data = JsonConvert.SerializeObject(content, setting);

            Console.WriteLine("----PluginBase.DataEvent:isStream:{0},content:{1}", isStream, content);

            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DataEvent, data));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
            await AddMessage(AuthorRole.Assistant.Label, string.Format(EventDataConstant.DataEvent, data));
        }
        public async Task DoneEvent(bool isStream)
        {
            if (isStream == false) return;

            Console.WriteLine("----PluginBase.DoneEvent:isStream:{0}", isStream);
            var bitys = Encoding.UTF8.GetBytes(string.Format(EventDataConstant.DoneEvent));
            await _httpContextAccessor.HttpContext.Response.Body.WriteAsync(bitys);
            await _httpContextAccessor.HttpContext.Response.Body.FlushAsync();
        }
        #endregion
    }
}
