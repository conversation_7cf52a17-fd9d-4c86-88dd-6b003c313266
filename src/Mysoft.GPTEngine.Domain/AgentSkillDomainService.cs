// Copyright (c) Microsoft. All rights reserved.

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Plugins.OpenApi;
using System.ClientModel.Primitives;
using Mysoft.GPTEngine.Common.CustomerException;
using Mysoft.GPTEngine.Common.Helper;
using Mysoft.GPTEngine.Domain;
using Mysoft.GPTEngine.Domain.AgentSkillEnhancement;
using Mysoft.GPTEngine.Domain.ApiAuthorization;
using Mysoft.GPTEngine.Domain.DTO;
using Mysoft.GPTEngine.Domain.Entity;
using Mysoft.GPTEngine.Domain.Shared.Enums;
using Mysoft.GPTEngine.SemanticKernel.Core.Qwen;
using Mysoft.GPTEngine.SemanticKernel.Core.Enums;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Services;
using Mysoft.GPTEngine.Domain.Shared;
using Mysoft.GPTEngine.Domain.Shared.Constants;
using Mysoft.GPTEngine.Domain.Shared.Dtos;
using Mysoft.GPTEngine.Domain.Shared.Utilities;
using Mysoft.GPTEngine.Domain.TextExtractDecode.ITextExtractDecode;
using Mysoft.GPTEngine.SemanticKernel.Core.Dtos;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Milvus.Client;
namespace Mysoft.GPTEngine.Application
{
#pragma warning disable SKEXP0110
#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0040

    public class AgentSkillDomainService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public readonly IMysoftContextFactory _mysoftContextFactory;
        public readonly IMapper _mapper;
        public readonly ILogger<AgentSkillDomainService> _logger;
        public ChatRunDto _chatRunDto;
        public Kernel _kernel;
        public CancellationToken _cancellationToken;
        private readonly MysoftApiService _mysoftApiDomainService;
        private readonly PluginRepostory _pluginRepostory;
        private readonly PluginMetadataRepostory _pluginMetadataRepostory;
        private readonly MysoftMemoryCache _mysoftMemoryCache;
        private readonly MysoftConfigurationDomain _mysoftConfigurationDomain;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly IConfigurationService _configurationService;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly AgentSkillEventHelper _eventHelper;
        private readonly ConversationMemoryService _conversationMemoryService;
        private readonly McpCustomService _mcpCustomService;
        private readonly McpServiceRepository _mcpServiceRepository;
        private readonly McpServiceToolRepository _mcpServiceToolRepository;
        private readonly KnowledgeRepository _knowledgeRepository;
        private readonly AgentServiceHelper _agentServiceHelper;
        private readonly ModelInstanceRepostory _modelInstanceRepostory;
        private readonly ModelRepostory _modelRepostory;

        /// <summary>
        /// 思考模式配置
        /// </summary>
        public ThinkingModeOptions ThinkingModeOptions { get; set; }

        public AgentSkillDomainService(IServiceProvider serviceProvider, ChatRunDto chatRunDto,
            ILogger<AgentSkillDomainService> logger)
        {
            _logger = logger;
            _chatRunDto = chatRunDto;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(IServiceProvider));
            _kernel = _serviceProvider.GetService<Kernel>() ?? throw new ArgumentNullException(nameof(Kernel));
            _mapper = _serviceProvider.GetService<IMapper>() ?? throw new ArgumentNullException(nameof(IMapper));
            _httpContextAccessor = _serviceProvider.GetService<IHttpContextAccessor>() ??
                                   throw new ArgumentNullException(nameof(IHttpContextAccessor));
            _mysoftContextFactory = _serviceProvider.GetService<IMysoftContextFactory>() ??
                                    throw new ArgumentNullException(nameof(IMysoftContextFactory));
            _mysoftApiDomainService = _serviceProvider.GetService<MysoftApiService>() ??
                                      throw new ArgumentNullException(nameof(MysoftApiService));
            _pluginRepostory = _serviceProvider.GetService<PluginRepostory>() ??
                               throw new ArgumentNullException(nameof(PluginRepostory));
            _pluginMetadataRepostory = _serviceProvider.GetService<PluginMetadataRepostory>() ??
                                       throw new ArgumentNullException(nameof(PluginMetadataRepostory));
            _mysoftMemoryCache = _serviceProvider.GetService<MysoftMemoryCache>() ??
                                 throw new ArgumentNullException(nameof(MysoftMemoryCache));
            _mysoftConfigurationDomain = _serviceProvider.GetService<MysoftConfigurationDomain>() ??
                                         throw new ArgumentNullException(nameof(MysoftConfigurationDomain));
            _knowledgeDomainService = _serviceProvider.GetService<IKnowledgeDomainService>() ??
                                      throw new ArgumentNullException(nameof(MysoftConfigurationDomain));
            _configurationService = _serviceProvider.GetService<IConfigurationService>() ??
                                    throw new ArgumentNullException(nameof(IConfigurationService));
            _knowledgeFileSectionRepository = _serviceProvider.GetService<KnowledgeFileSectionRepository>() ??
                                              throw new ArgumentNullException(nameof(KnowledgeFileSectionRepository));
            _eventHelper = new AgentSkillEventHelper(_httpContextAccessor);
            _conversationMemoryService = _serviceProvider.GetService<ConversationMemoryService>() ??
                                        throw new ArgumentNullException(nameof(ConversationMemoryService));
            _mcpCustomService = _serviceProvider.GetService<McpCustomService>() ??
                               throw new ArgumentNullException(nameof(McpCustomService));
            _mcpServiceRepository = _serviceProvider.GetService<McpServiceRepository>() ??
                                   throw new ArgumentNullException(nameof(McpServiceRepository));
            _mcpServiceToolRepository = _serviceProvider.GetService<McpServiceToolRepository>() ??
                                       throw new ArgumentNullException(nameof(McpServiceToolRepository));
            _knowledgeRepository = _serviceProvider.GetService<KnowledgeRepository>() ??
                                  throw new ArgumentNullException(nameof(KnowledgeRepository));
            _agentServiceHelper = new AgentServiceHelper(
                _knowledgeDomainService,
                _kernel,
                _mysoftApiDomainService,
                _mysoftContextFactory,
                _httpContextAccessor,
                _mapper,
                _serviceProvider.GetService<ILogger<AgentServiceHelper>>(),
                _knowledgeRepository);
            _modelInstanceRepostory = _serviceProvider.GetService<ModelInstanceRepostory>() ??
                                     throw new ArgumentNullException(nameof(ModelInstanceRepostory));
            _modelRepostory = _serviceProvider.GetService<ModelRepostory>() ??
                             throw new ArgumentNullException(nameof(ModelRepostory));


        }

        /// <summary>
        /// 获取模型实例，如果当前模型为null则返回默认模型
        /// </summary>
        /// <returns>模型实例</returns>
        private async Task<ModelInstanceDto> GetModelInstanceAsync()
        {
            if (_chatRunDto.ModelInstance != null)
            {
                return _chatRunDto.ModelInstance;
            }

            _logger.LogInformation("[GetModelInstanceAsync] _chatRunDto.ModelInstance为null，尝试获取默认模型");

            // 获取InstanceCode为default_text_generation的默认模型
            var defaultModelInstance = await _modelInstanceRepostory.GetFirstAsync(x =>
                x.InstanceCode == "default_text_generation" && x.IsDefault && x.IsAvailable);

            if (defaultModelInstance != null)
            {
                // 查询关联的ModelEntity来获取正确的ModelCode
                var modelEntity = await _modelRepostory.GetFirstAsync(x => x.ModelGUID == defaultModelInstance.ModelGUID);

                var modelInstanceDto = _mapper.Map<ModelInstanceDto>(defaultModelInstance);

                // 修复ModelCode赋值逻辑：优先使用CustomModelCode，如果为空则使用ModelEntity的ModelCode
                if (modelEntity != null)
                {
                    modelInstanceDto.ModelCode = !string.IsNullOrEmpty(defaultModelInstance.CustomModelCode) ?
                        defaultModelInstance.CustomModelCode : modelEntity.ModelCode;
                    modelInstanceDto.ModelType = modelEntity.ModelType;
                    modelInstanceDto.ServiceTypeEnum = modelEntity.ServiceTypeEnum;
                }

                _logger.LogInformation("[GetModelInstanceAsync] 找到默认模型: {instanceName}, InstanceCode: {instanceCode}",
                    modelInstanceDto.InstanceName, modelInstanceDto.InstanceCode);

                return modelInstanceDto;
            }

            _logger.LogWarning("[GetModelInstanceAsync] 未找到InstanceCode为default_text_generation的默认模型");

            // 如果没有找到指定的默认模型，尝试获取任何默认的文本生成模型
            var fallbackDefaultModels = await _modelInstanceRepostory.GetListAsync(x => x.IsDefault && x.IsAvailable);
            if (fallbackDefaultModels != null && fallbackDefaultModels.Count > 0)
            {
                var modeGuids = fallbackDefaultModels.Select(x => x.ModelGUID).ToList();
                var model = await _modelRepostory.GetFirstAsync(x => modeGuids.Contains(x.ModelGUID) &&
                    x.ServiceTypeEnum == ServiceTypeEnum.TextGeneration);

                if (model != null)
                {
                    var fallbackInstance = fallbackDefaultModels.FirstOrDefault(x => x.ModelGUID == model.ModelGUID);
                    if (fallbackInstance != null)
                    {
                        var modelInstanceDto = _mapper.Map<ModelInstanceDto>(fallbackInstance);
                        modelInstanceDto.ModelCode = !string.IsNullOrEmpty(fallbackInstance.CustomModelCode) ?
                            fallbackInstance.CustomModelCode : model.ModelCode;
                        modelInstanceDto.ModelType = model.ModelType;
                        modelInstanceDto.ServiceTypeEnum = model.ServiceTypeEnum;

                        _logger.LogInformation("[GetModelInstanceAsync] 使用备用默认模型: {instanceName}, InstanceCode: {instanceCode}",
                            modelInstanceDto.InstanceName, modelInstanceDto.InstanceCode);

                        return modelInstanceDto;
                    }
                }
            }

            _logger.LogError("[GetModelInstanceAsync] 无法找到任何可用的默认模型");
            throw new BusinessException("无法找到可用的默认模型，请检查模型配置");
        }

        public async Task ChatCompletionStream(CancellationToken cancellationToken = default)
        {
            //_chatRunDto = _httpContextAccessor.GetItem<ChatRunDto>(nameof(ChatRunDto)) ?? throw new ArgumentNullException(nameof(ChatRunDto));
            if (_chatRunDto.Cancel) return;
            _cancellationToken = cancellationToken;
            
            // 创建工具标题映射Dictionary，确保线程安全
            var toolTitleMapping = new ConcurrentDictionary<string, string>();

            // 将映射存储到HttpContext中，供PluginFilter使用
            _httpContextAccessor.HttpContext.Items["ToolTitleMapping"] = toolTitleMapping;

            // 清除之前的ChatMessages，避免消息累积污染
            ClearChatMessages();

            _agentServiceHelper.AddNodeLog(_chatRunDto);
            var input = await _chatRunDto.GetArgumentValue<string>(KernelArgumentsConstant.Input);
            _logger.LogInformation("[ChatCompletionStream] input: {@input}", input);

            // 处理知识库类型检查和向量库全量数据查询（仅用于日志记录，不进行工具注入）
            await ProcessKnowledgeTypesAsync();


            var agentDto = _chatRunDto.Agent;
            _logger.LogInformation("[ChatCompletionStream] Agent配置状态: {agentIsNull}", agentDto == null ? "null" : "not null");
            if (agentDto != null)
            {
                _logger.LogInformation("[ChatCompletionStream] Agent详细信息: Tools数量={toolsCount}, Knowledgs数量={knowledgsCount}, Mcps数量={mcpsCount}",
                    agentDto.Tools?.Count ?? 0,
                    agentDto.Knowledgs?.Count ?? 0,
                    agentDto.Mcps?.Count ?? 0);

                // 序列化Agent对象查看完整内容
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };
                    var agentJson = JsonSerializer.Serialize(agentDto, options);
                    _logger.LogInformation("[ChatCompletionStream] Agent完整JSON内容: {agentJson}", agentJson);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[ChatCompletionStream] 序列化Agent对象失败");
                }
            }

            // 根据模型支持情况和Agent配置决定思考模式
            await ConfigureThinkingModeWithModelCheckAsync();
            Kernel kernel = await CreateKernelWithTool(toolTitleMapping, ThinkingModeOptions);
            _logger.LogInformation("[ChatCompletionStream] Kernel已注册插件列表: {plugins}",
                string.Join(", ", kernel.Plugins.Select(p => p.Name)));

            // 在Semantic Kernel 1.60中，我们使用传统的ChatCompletion服务
            var chatCompletionService = kernel.GetRequiredService<IChatCompletionService>();

            DocumentReadingDto documentReadingDto = await _agentServiceHelper.DocumentReading(_chatRunDto);

            // 创建一个全新的ChatHistory，确保不受其他地方影响
            ChatHistory chat = new ChatHistory();
            _logger.LogInformation("[ChatCompletionStream] 创建新的ChatHistory，初始消息数: {count}", chat.Count);

            // 获取用户GUID和会话GUID作为会话标识符
            string userGuid = _chatRunDto.Chat?.UserGUID ?? string.Empty;
            string chatGuid = _chatRunDto.Chat?.ChatGUID.ToString() ?? string.Empty;
            _logger.LogInformation("[ChatCompletionStream] 用户GUID: {userGuid}, 会话GUID: {chatGuid}", userGuid, chatGuid);

            // 获取技能Agent配置以获取maxTurn配置
            int maxTurn = GetMaxTurnFromSkillAgent();
            _logger.LogInformation("[ChatCompletionStream] 从SkillAgentDto.MaxResultNumber获取maxTurn: {maxTurn}", maxTurn);

           
            // 先准备系统提示
            await _agentServiceHelper.ArgumentParser(_chatRunDto, _chatRunDto.Agent.Prompt.Inputs);
            var arguments = await _agentServiceHelper.GetAgentInputArgument(_chatRunDto);
            // 打印arguments参数
            _logger.LogInformation("[ChatCompletionStream] arguments: {@arguments}", arguments);

            // 检查prompt模板是否为空，如果为空则使用默认提示词
            var promptTemplate = agentDto.Prompt.Template;
            if (string.IsNullOrWhiteSpace(promptTemplate))
            {
                promptTemplate = "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。";
                _logger.LogInformation("[ChatCompletionStream] Prompt模板为空，使用默认提示词");
            }

            var systemPrompt =
                await _agentServiceHelper.TemplateRenderAsync(promptTemplateText: promptTemplate, arguments: arguments);

            var prompt = systemPrompt.Content + documentReadingDto.Content;
            // 打印prompt内容
            _logger.LogInformation("[ChatCompletionStream] prompt: {prompt}", prompt);

            // 首先添加系统提示（确保在最开头）
            chat.AddMessage(AuthorRole.System, prompt);
            _logger.LogInformation("[ChatCompletionStream] 已添加系统提示，当前ChatHistory消息数: {count}", chat.Count);

            // 如果有用户GUID和会话GUID，加载历史会话记忆（只加载用户-助手对话，不包含系统提示）
            if (!string.IsNullOrEmpty(userGuid) && !string.IsNullOrEmpty(chatGuid))
            {
                chat = await _conversationMemoryService.LoadConversationMemoryAsync(chat, userGuid, chatGuid, maxTurn);
                _logger.LogInformation("[ChatCompletionStream] 已为用户 {userGuid} 会话 {chatGuid} 加载会话记忆，使用maxTurn: {maxTurn}，当前ChatHistory消息数: {count}", userGuid, chatGuid, maxTurn, chat.Count);
            }

            // 验证和修复ChatHistory中的工具调用序列
            chat = chat.ValidateAndFixToolCalls(_logger);
            _logger.LogInformation("[ChatCompletionStream] 验证和修复工具调用序列后，ChatHistory消息数: {count}", chat.Count);

            // 最后添加当前用户消息
            chat.AddMessage(AuthorRole.User, _chatRunDto.ChatInput.Input);

            _logger.LogInformation("[ChatCompletionStream] 添加系统提示和用户消息后，ChatHistory消息数: {count}", chat.Count);

            // 保存ChatHistory到ChatRunDto，并记录初始长度（用于后续提取工具调用消息）
            _chatRunDto.ChatHistory = chat;
            _chatRunDto.InitialChatHistoryCount = chat.Count;
            _logger.LogInformation("[ChatCompletionStream] 记录初始ChatHistory长度: {count}", _chatRunDto.InitialChatHistoryCount);

            // 打印ChatHistory的内容用于调试
            for (int i = 0; i < chat.Count; i++)
            {
                var message = chat[i];
                var contentPreview = message.Content != null
                    ? message.Content.Substring(0, Math.Min(100, message.Content.Length)) + "..."
                    : "null";

                // 检查是否有工具调用
                if (message.Items != null && message.Items.Any())
                {
                    var toolCalls = message.Items.OfType<FunctionCallContent>().ToList();
                    var toolResults = message.Items.OfType<FunctionResultContent>().ToList();

                    if (toolCalls.Any())
                    {
                        _logger.LogInformation("[ChatCompletionStream] ChatHistory[{index}] - Role: {role}, Content: {content}, ToolCalls: {toolCallCount}, IDs: [{ids}]",
                            i, message.Role, contentPreview, toolCalls.Count, string.Join(", ", toolCalls.Select(tc => tc.Id)));
                    }
                    else if (toolResults.Any())
                    {
                        _logger.LogInformation("[ChatCompletionStream] ChatHistory[{index}] - Role: {role}, Content: {content}, ToolResults: {toolResultCount}, IDs: [{ids}]",
                            i, message.Role, contentPreview, toolResults.Count, string.Join(", ", toolResults.Select(tr => tr.CallId)));
                    }
                    else
                    {
                        _logger.LogInformation("[ChatCompletionStream] ChatHistory[{index}] - Role: {role}, Content: {content}, Items: {itemCount}",
                            i, message.Role, contentPreview, message.Items.Count);
                    }
                }
                else
                {
                    _logger.LogInformation("[ChatCompletionStream] ChatHistory[{index}] - Role: {role}, Content: {content}",
                        i, message.Role, contentPreview);
                }
            }

            await InvokeAgentAsync(prompt, _chatRunDto.ChatInput.Input);

            async Task InvokeAgentAsync(string sysPrompt, string userInput = "")
            {
                _logger.LogInformation("[InvokeAgentAsync] 入参 prompt: {prompt}, userInput: {userInput}", sysPrompt,
                    userInput);
                if (string.IsNullOrEmpty(prompt))
                {
                    throw new NoRequiredArgumentException("智能体技能的Prompt不能为空");
                }

                // 注意：不再在这里添加系统提示和用户消息，因为已经在外层添加过了
                string chatOutput = "";
                var executionSettings = await CreateExecutionSettingsFromAgentAsync();

                // 在Semantic Kernel 1.60中，使用ChatCompletion服务
                IAsyncEnumerable<StreamingChatMessageContent> responed = chatCompletionService.GetStreamingChatMessageContentsAsync(
                    chat, executionSettings, kernel, cancellationToken);
                await foreach (StreamingChatMessageContent response in responed)
                {
                    // 调试日志：检查思考模式配置
                    // _logger.LogDebug("[InvokeAgentAsync] ThinkingModeOptions: {thinkingOptions}, EnableThinking: {enableThinking}",
                        // ThinkingModeOptions != null, ThinkingModeOptions?.EnableThinking);
                    // Console.WriteLine($"[DEBUG] ThinkingModeOptions: {ThinkingModeOptions != null}, EnableThinking: {ThinkingModeOptions?.EnableThinking}");

                    // 处理思考模式下的思考输出
                    if (ThinkingModeOptions?.EnableThinking == true)
                    {
                        Console.WriteLine($"[DEBUG] 进入思考内容提取逻辑");
                        var thinkingContent = _agentServiceHelper.ExtractThinkingContent(response, _eventHelper, _cancellationToken);
                        if (!string.IsNullOrEmpty(thinkingContent))
                        {
                            _logger.LogInformation("[InvokeAgentAsync] 思考内容: {thinkingContent}", thinkingContent);
                            Console.WriteLine($"🤔 思考过程: {thinkingContent}");
                        }
                    }
                    else
                    {
                        // Console.WriteLine($"[DEBUG] 思考模式未启用或配置为空");
                    }

                    if (response?.Content != null)
                    {
                        string content = response.Content;
                        WriteAgentChatMessage(response);
                        chatOutput += content;
                    }
                }
                // 修复：直接保存纯净的chatOutput，不使用EventDataConstant.TextEvent格式

                _agentServiceHelper.AddSucceedNodeLog(_chatRunDto, userInput, chatOutput);

                // 保存大模型输出到用户消息记录的Answer字段
                await UpdateUserMessageWithAnswerAsync(userInput, chatOutput, userGuid, chatGuid);

                // 保存会话记忆（包含工具调用）
                if (!string.IsNullOrEmpty(userGuid) && !string.IsNullOrEmpty(chatGuid))
                {
                    // 获取ChatHistory中新增的消息（包含工具调用和工具返回）
                    var newMessages = _agentServiceHelper.ExtractNewMessagesFromChatHistory(_chatRunDto, userInput, chatOutput);
                    if (newMessages.Any())
                    {
                        // 获取租户信息和用户名
                        var tenantCode = _chatRunDto?.Chat?.TenantCode ?? string.Empty;
                        var tenantName = _chatRunDto?.Chat?.TenantName ?? string.Empty;
                        var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;
                        var batchGuid = _chatRunDto?.BatchGuid ?? Guid.NewGuid();

                        await _conversationMemoryService.SaveConversationMessagesAsync(userGuid, chatGuid, newMessages, tenantCode, tenantName, batchGuid, userName);
                        _logger.LogInformation("[InvokeAgentAsync] 已为用户 {userGuid} 会话 {chatGuid} 保存 {count} 条会话记忆（包含工具调用），租户: {tenantCode}，用户名: {userName}，BatchGUID: {batchGuid}", userGuid, chatGuid, newMessages.Count, tenantCode, userName, batchGuid);
                    }
                    else
                    {
                        // 如果没有工具调用，使用原来的简单保存方式
                        var tenantCode = _chatRunDto?.Chat?.TenantCode ?? string.Empty;
                        var tenantName = _chatRunDto?.Chat?.TenantName ?? string.Empty;
                        var userName = _chatRunDto?.Chat?.UserName ?? string.Empty;
                        var batchGuid = _chatRunDto?.BatchGuid ?? Guid.NewGuid();

                        await _conversationMemoryService.SaveConversationMemoryAsync(userGuid, chatGuid, userInput, chatOutput, tenantCode, tenantName, batchGuid, userName);
                        _logger.LogInformation("[InvokeAgentAsync] 已为用户 {userGuid} 会话 {chatGuid} 保存简单会话记忆，租户: {tenantCode}，用户名: {userName}，BatchGUID: {batchGuid}", userGuid, chatGuid, tenantCode, userName, batchGuid);
                    }
                }
            }
        }

        
        /// <summary>
        /// 从SkillAgentDto的MaxResultNumber字段中获取maxTurn配置
        /// </summary>
        /// <returns>maxTurn值，如果未配置则返回默认值20</returns>
        private int GetMaxTurnFromSkillAgent()
        {
            try
            {
                var agent = _chatRunDto.Agent;
                if (agent == null)
                {
                    _logger.LogWarning("[GetMaxTurnFromSkillAgent] Agent为空，使用默认maxTurn: 20");
                    return 20;
                }

                int maxTurn = agent.MaxResultNumber > 0 ? agent.MaxResultNumber : 20;
                _logger.LogInformation("[GetMaxTurnFromSkillAgent] 从SkillAgentDto.MaxResultNumber获取maxTurn: {maxTurn}", maxTurn);
                return maxTurn;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GetMaxTurnFromSkillAgent] 获取SkillAgentDto.MaxResultNumber时发生错误，使用默认值20");
                return 20;
            }
        }
        
        private void WriteAgentChatMessage(StreamingChatMessageContent response)
        {
            if (response == null || response.Content == null) return;
            Console.WriteLine($"assistant=== {response}");
            _eventHelper.TextEvent(response.Content, _cancellationToken).GetAwaiter().GetResult();
        }
        

        /// <summary>
        /// 清除ChatRunDto中的ChatMessages，防止消息累积污染
        /// </summary>
        private void ClearChatMessages()
        {
            if (_chatRunDto?.ChatMessages != null)
            {
                var messageCount = _chatRunDto.ChatMessages.Count;
                _chatRunDto.ChatMessages.Clear();
                _logger.LogInformation("[ClearChatMessages] 已清除 {count} 条ChatMessages", messageCount);
            }
        }
        
        

        private async Task<Kernel> CreateKernelWithTool(ConcurrentDictionary<string, string> toolTitleMapping, ThinkingModeOptions thinkingOptions = null)
        {
            _logger.LogInformation("[CreateKernelWithTool] 进入方法");
            var skillModelInstance = await GetModelInstanceAsync();
            _logger.LogInformation("[skillModelInstance tool] {@skillModelInstance}", skillModelInstance.IsSupportTool);
            _logger.LogInformation("[skillModelInstance SupportDeepThink] {@SupportDeepThink}", skillModelInstance.SupportDeepThink);

            IKernelBuilder builder = Kernel.CreateBuilder();
            // 添加PluginFilter
            builder.Services.AddSingleton<IFunctionInvocationFilter>(sp =>
            {
                var loggerFactory = sp.GetService<ILoggerFactory>();
                var pluginLogger = loggerFactory?.CreateLogger<PluginFilter>();
                return new PluginFilter(_httpContextAccessor, pluginLogger);
            });
            List<ModelInstanceDto> textModelInstances = new List<ModelInstanceDto>();
            textModelInstances.Add(skillModelInstance);

            // 传递思考模式配置到AddChatCompletionServices
            builder.AddChatCompletionServices(textModelInstances, useAgent: true, thinkingOptions).GetAwaiter().GetResult();

            var kernel = builder.Build();
            _logger.LogInformation("[CreateKernelWithTool] Kernel构建完成，开始添加插件");

            _logger.LogInformation("[CreateKernelWithTool] 开始添加API插件");
            await AddApiPlugin(kernel, toolTitleMapping);
            _logger.LogInformation("[CreateKernelWithTool] API插件添加完成");

            _logger.LogInformation("[CreateKernelWithTool] 开始添加知识库插件");
            await AddKnowLedgePlugin(kernel, toolTitleMapping);
            _logger.LogInformation("[CreateKernelWithTool] 知识库插件添加完成");

            _logger.LogInformation("[CreateKernelWithTool] 开始添加数据知识库插件");
            await AddDataKnowledgePlugin(kernel, toolTitleMapping);
            _logger.LogInformation("[CreateKernelWithTool] 数据知识库插件添加完成");
          

            // 添加MCP工具
            _logger.LogInformation("[CreateKernelWithTool] 开始添加MCP工具");
            await AddMcpToolsWithCustomService(kernel, toolTitleMapping);
            _logger.LogInformation("[CreateKernelWithTool] MCP工具添加完成");

            // 验证所有工具名称长度
            _agentServiceHelper.ValidateToolNameLengths(kernel);

            return kernel;
        }

        

        public async Task AddKnowLedgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            await AddKnowLedgePlugin(kernel, toolTitleMapping, 0.1);
        }

        public async Task AddKnowLedgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping, double minRelevanceScore)
        {
            var agent = _chatRunDto.Agent;
            if (agent == null || agent.Knowledgs == null)
            {
                return;
            }

            // 批量查询所有知识库的TypeEnum信息
            var knowledgeTypeMap = await _agentServiceHelper.GetKnowledgeTypeMapAsync(agent.Knowledgs.Select(k => k.Code).ToArray());

            // 过滤出TypeEnum为1的知识库，排除TypeEnum为3的知识库
            var filteredKnowledges = new List<AgentKnowledgeDto>();

            foreach (var knowledge in agent.Knowledgs)
            {
                try
                {
                    if (!knowledgeTypeMap.TryGetValue(knowledge.Code, out var typeEnum))
                    {
                        _logger.LogWarning("[AddKnowLedgePlugin] 未找到知识库实体: {code}", knowledge.Code);
                        continue;
                    }

                    if (typeEnum == 1)
                    {
                        filteredKnowledges.Add(knowledge);
                        _logger.LogInformation("[AddKnowLedgePlugin] 添加TypeEnum=1的知识库到KnowledgeToolsImporter: {code}", knowledge.Code);
                    }
                    else if (typeEnum == 3)
                    {
                        _logger.LogInformation("[AddKnowLedgePlugin] 跳过TypeEnum=3的知识库，将由专门逻辑处理: {code}", knowledge.Code);
                    }
                    else
                    {
                        _logger.LogInformation("[AddKnowLedgePlugin] 跳过TypeEnum={typeEnum}的知识库: {code}", typeEnum, knowledge.Code);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[AddKnowLedgePlugin] 处理知识库 {code} 时发生异常: {message}", knowledge.Code, ex.Message);
                }
            }

            if (filteredKnowledges.Any())
            {
                _logger.LogInformation("[AddKnowLedgePlugin] 向KnowledgeToolsImporter传递 {count} 个TypeEnum=1的知识库", filteredKnowledges.Count);
                await new KnowledgeToolsImporter(kernel, _serviceProvider, minRelevanceScore).ImportKnowledgeTools(filteredKnowledges, toolTitleMapping);
            }
            else
            {
                _logger.LogInformation("[AddKnowLedgePlugin] 没有TypeEnum=1的知识库需要处理");
            }
        }

        public async Task AddApiPlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            await new ApiToolsImporter(kernel, _serviceProvider, _chatRunDto).ImportApiTools(toolTitleMapping);
        }

       
        /// <summary>
        /// 添加数据知识库插件（TypeEnum=3的知识库）
        /// </summary>
        /// <param name="kernel">Kernel实例</param>
        public async Task AddDataKnowledgePlugin(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            _logger.LogInformation("[AddDataKnowledgePlugin] 方法开始执行");
            try
            {
                var agent = _chatRunDto.Agent;
                if (agent?.Knowledgs == null || !agent.Knowledgs.Any())
                {
                    _logger.LogInformation("[AddDataKnowledgePlugin] 没有配置知识库，跳过处理");
                    // return;
                }

                // 批量查询所有知识库的TypeEnum信息
                var knowledgeTypeMap = await _agentServiceHelper.GetKnowledgeTypeMapAsync(agent.Knowledgs.Select(k => k.Code).ToArray());

                // 收集所有TypeEnum=3的知识库代码
                var typeEnum3KnowledgeCodes = new List<string>();

                foreach (var knowledge in agent.Knowledgs)
                {
                    if (knowledgeTypeMap.TryGetValue(knowledge.Code, out var typeEnum) && typeEnum == 3)
                    {
                        typeEnum3KnowledgeCodes.Add(knowledge.Code);
                        _logger.LogInformation("[AddDataKnowledgePlugin] 添加TypeEnum=3的知识库: {code}", knowledge.Code);
                    }
                }

                if (typeEnum3KnowledgeCodes.Any())
                {
                    _logger.LogInformation("[AddDataKnowledgePlugin] 开始处理 {count} 个TypeEnum=3的知识库", typeEnum3KnowledgeCodes.Count);
                    var dataKnowledgeImporter = new DataKnowledgeImporter(kernel, _serviceProvider);
                    await dataKnowledgeImporter.QueryAllVectorDatabaseDataAsync(typeEnum3KnowledgeCodes.ToArray(), toolTitleMapping);
                    _logger.LogInformation("[AddDataKnowledgePlugin] TypeEnum=3知识库处理完成");
                }
                else
                {
                    _logger.LogInformation("[AddDataKnowledgePlugin] 没有TypeEnum=3的知识库需要处理");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddDataKnowledgePlugin] 添加数据知识库插件时发生异常: {message}", ex.Message);
            }
        }
        
        

        /// <summary>
        /// 使用McpCustomService添加MCP工具
        /// </summary>
        private async Task AddMcpToolsWithCustomService(Kernel kernel, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                _logger.LogInformation("[AddMcpToolsWithCustomService] 使用McpToolsImporter加载MCP工具");

                var agent = _chatRunDto.Agent;
                _logger.LogInformation("[AddMcpToolsWithCustomService] Agent对象状态: {agentIsNull}", agent == null ? "null" : "not null");

                if (agent != null)
                {
                    _logger.LogInformation("[AddMcpToolsWithCustomService] Agent.Mcps状态: {mcpsIsNull}, 数量: {count}",
                        agent.Mcps == null ? "null" : "not null",
                        agent.Mcps?.Count ?? 0);

                    if (agent.Mcps != null)
                    {
                        _logger.LogInformation("[AddMcpToolsWithCustomService] MCP工具详细信息: {@mcps}", agent.Mcps);
                    }
                }

                if (agent == null || agent.Mcps == null || !agent.Mcps.Any())
                {
                    _logger.LogInformation("[AddMcpToolsWithCustomService] Agent中没有配置MCP工具，跳过加载");
                    return;
                }

                // 使用McpToolsImporter导入MCP工具
                await new McpToolsImporter(kernel, _serviceProvider).ImportMcpTools(agent.Mcps, toolTitleMapping);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[AddMcpToolsWithCustomService] 使用McpToolsImporter添加MCP工具时发生异常: {message}", ex.Message);
            }
        }



        /// <summary>
        /// 配置思考模式
        /// </summary>
        /// <param name="enableThinking">是否启用思考模式</param>
        /// <param name="thinkingBudget">思考预算</param>
        /// <param name="extraParameters">额外参数</param>
        public void ConfigureThinkingMode(bool enableThinking, int? thinkingBudget = null, Dictionary<string, object> extraParameters = null)
        {
            ThinkingModeOptions = new ThinkingModeOptions
            {
                EnableThinking = enableThinking,
                ThinkingBudget = thinkingBudget,
                ExtraParameters = extraParameters ?? new Dictionary<string, object>()
            };

            _logger.LogInformation("[ConfigureThinkingMode] 思考模式配置已更新 - EnableThinking: {enableThinking}, ThinkingBudget: {thinkingBudget}",
                enableThinking, thinkingBudget);
        }
        



        /// <summary>
        /// 处理知识库类型检查和向量库全量数据查询
        /// </summary>
        private async Task ProcessKnowledgeTypesAsync()
        {
            try
            {
                // 注意：这里不进行工具注入，因为kernel还未创建
                // 工具注入将在CreateKernelWithTool方法中进行

                var agent = _chatRunDto.Agent;
                if (agent?.Knowledgs == null || !agent.Knowledgs.Any())
                {
                    _logger.LogInformation("[ProcessKnowledgeTypesAsync] 没有配置知识库，跳过处理");
                    return;
                }

                _logger.LogInformation("[ProcessKnowledgeTypesAsync] 开始处理 {count} 个知识库", agent.Knowledgs.Count);

                // 批量查询所有知识库的TypeEnum信息
                var knowledgeTypeMap = await _agentServiceHelper.GetKnowledgeTypeMapAsync(agent.Knowledgs.Select(k => k.Code).ToArray());

                // 收集所有TypeEnum=3的知识库代码
                var typeEnum3KnowledgeCodes = new List<string>();

                foreach (var knowledge in agent.Knowledgs)
                {
                    try
                    {
                        _logger.LogInformation("[ProcessKnowledgeTypesAsync] 处理知识库: {code}, {name}", knowledge.Code, knowledge.Name);

                        if (!knowledgeTypeMap.TryGetValue(knowledge.Code, out var typeEnum))
                        {
                            _logger.LogWarning("[ProcessKnowledgeTypesAsync] 未找到知识库: {code}", knowledge.Code);
                            continue;
                        }

                        _logger.LogInformation("[ProcessKnowledgeTypesAsync] 知识库 {code} 的TypeEnum: {typeEnum}", knowledge.Code, typeEnum);

                        if (typeEnum == 3)
                        {
                            _logger.LogInformation("[ProcessKnowledgeTypesAsync] 知识库 {code} 类型为3，添加到批量查询列表", knowledge.Code);
                            typeEnum3KnowledgeCodes.Add(knowledge.Code);
                        }
                        else if (typeEnum == 1)
                        {
                            _logger.LogInformation("[ProcessKnowledgeTypesAsync] 知识库 {code} 类型为1，将使用现有KnowledgeToolsImporter逻辑", knowledge.Code);
                        }
                        else
                        {
                            _logger.LogInformation("[ProcessKnowledgeTypesAsync] 知识库 {code} 类型为 {typeEnum}，暂不处理", knowledge.Code, typeEnum);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[ProcessKnowledgeTypesAsync] 处理知识库 {code} 时发生异常: {message}", knowledge.Code, ex.Message);
                    }
                }

                // 记录TypeEnum=3的知识库数量，实际工具注入将在CreateKernelWithTool中进行
                if (typeEnum3KnowledgeCodes.Any())
                {
                    _logger.LogInformation("[ProcessKnowledgeTypesAsync] 发现 {count} 个TypeEnum=3的知识库，将在CreateKernelWithTool中进行工具注入", typeEnum3KnowledgeCodes.Count);
                }
                else
                {
                    _logger.LogInformation("[ProcessKnowledgeTypesAsync] 没有TypeEnum=3的知识库");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessKnowledgeTypesAsync] 处理知识库类型时发生异常: {message}", ex.Message);
            }
        }



        /// <summary>
        /// 从Agent的executionSetting创建PromptExecutionSettings
        /// </summary>
        /// <returns>配置好的PromptExecutionSettings</returns>
        private async Task<OpenAIPromptExecutionSettings> CreateExecutionSettingsFromAgentAsync()
        {
            var executionSettings = new OpenAIPromptExecutionSettings
            {
                FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
            };

            try
            {
                var agentExecutionSetting = _chatRunDto.Agent?.executionSetting;
                var modelInstance = await GetModelInstanceAsync();

                _logger.LogInformation("[CreateExecutionSettingsFromAgent] 开始处理executionSetting，模型类型: {modelType}", modelInstance?.ModelType);

                // 处理temperature参数（支持0值，默认值通常为1.0）
                float temperature = AgentServiceHelper.GetTemperatureParameter(agentExecutionSetting, _logger);
                if (temperature >= 0 && temperature <= 2.0f) // 大多数模型支持0-2范围
                {
                    executionSettings.Temperature = temperature;
                    _logger.LogInformation("[CreateExecutionSettingsFromAgent] 设置Temperature: {temperature}", temperature);
                }
                else
                {
                    _logger.LogWarning("[CreateExecutionSettingsFromAgent] Temperature值超出范围(0-2): {temperature}，使用默认值", temperature);
                }

                // 处理top_p参数（支持0值，默认值通常为1.0）
                float topP = AgentServiceHelper.GetTopPParameter(agentExecutionSetting, _logger);
                if (topP >= 0 && topP <= 1.0f) // top_p范围是0-1，包含0值
                {
                    executionSettings.TopP = topP;
                    _logger.LogInformation("[CreateExecutionSettingsFromAgent] 设置TopP: {topP}", topP);
                }
                else if (topP > 1.0f)
                {
                    _logger.LogWarning("[CreateExecutionSettingsFromAgent] TopP值超出范围(0-1): {topP}，跳过设置", topP);
                }
                // topP == -1 时（未设置）不做任何处理，使用系统默认值

                // 处理max_tokens参数 - 阿里云模型需要特殊处理
                int maxTokens = AgentServiceHelper.GetMaxTokensParameter(agentExecutionSetting, _logger);
                if (maxTokens > 0)
                {
                    if (modelInstance?.ModelType == ModelTypeEnum.Ali)
                    {
                        // 阿里云模型：不设置MaxTokens，而是通过ExtraParameters传递max_tokens
                        HandleAliMaxTokensParameter(maxTokens);
                        _logger.LogInformation("[CreateExecutionSettingsFromAgent] 阿里云模型：通过ExtraParameters设置max_tokens: {maxTokens}", maxTokens);
                    }
                    else
                    {
                        // 其他模型：正常设置MaxTokens（会生成max_completion_tokens参数）
                        executionSettings.MaxTokens = maxTokens;
                        _logger.LogInformation("[CreateExecutionSettingsFromAgent] 非阿里云模型：设置MaxTokens: {maxTokens}", maxTokens);
                    }
                }

                _logger.LogInformation("[CreateExecutionSettingsFromAgent] 最终PromptExecutionSettings: Temperature={temperature}, TopP={topP}, MaxTokens={maxTokens}",
                    executionSettings.Temperature, executionSettings.TopP, executionSettings.MaxTokens);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CreateExecutionSettingsFromAgent] 处理Agent executionSetting时发生错误，使用默认配置: {message}", ex.Message);
            }

            return executionSettings;
        }


        
        /// <summary>
        /// 处理阿里云模型的max_tokens参数
        /// 阿里云模型不支持max_completion_tokens，需要使用max_tokens参数
        /// </summary>
        /// <param name="maxTokens">最大token数</param>
        private void HandleAliMaxTokensParameter(int maxTokens)
        {
            try
            {
                // 确保ThinkingModeOptions已初始化
                if (ThinkingModeOptions == null)
                {
                    ThinkingModeOptions = new ThinkingModeOptions();
                }

                // 确保ExtraParameters已初始化
                if (ThinkingModeOptions.ExtraParameters == null)
                {
                    ThinkingModeOptions.ExtraParameters = new Dictionary<string, object>();
                }

                // 添加max_tokens参数到ExtraParameters
                ThinkingModeOptions.ExtraParameters["max_tokens"] = maxTokens;

                _logger.LogInformation("[HandleAliMaxTokensParameter] 为阿里云模型添加max_tokens参数: {maxTokens}", maxTokens);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[HandleAliMaxTokensParameter] 处理阿里云模型max_tokens参数时发生错误: {message}", ex.Message);
            }
        }





        /// <summary>
        /// 根据模型支持情况和Agent配置决定思考模式
        /// 必须同时满足：modelInstance.SupportDeepThink==1 且 executionSetting.enableThinking==true
        /// </summary>
        private async Task ConfigureThinkingModeWithModelCheckAsync()
        {
            try
            {
                var modelInstance = await GetModelInstanceAsync();
                var agentExecutionSetting = _chatRunDto.Agent?.executionSetting;

                // 记录模型和Agent配置信息
                _logger.LogInformation("[ConfigureThinkingMode] 模型信息: {instanceName}, SupportDeepThink={supportDeepThink}",
                    modelInstance?.InstanceName ?? "Unknown", modelInstance?.SupportDeepThink ?? 0);

                if (agentExecutionSetting != null)
                {
                    _logger.LogInformation("[ConfigureThinkingMode] Agent executionSetting: temperature={temperature}, topP={topP}, maxTokens={maxTokens}, enableThinking={enableThinking}, thinkingBudget={thinkingBudget}",
                        agentExecutionSetting.temperature, agentExecutionSetting.topP, agentExecutionSetting.maxTokens, agentExecutionSetting.enableThinking, agentExecutionSetting.thinkingBudget);
                }

                // 检查思考模式启用条件：模型支持 AND Agent配置启用
                bool modelSupportsThinking = modelInstance?.SupportDeepThink == 1;
                bool agentEnablesThinking = AgentServiceHelper.GetEnableThinkingParameter(agentExecutionSetting, _logger);
                bool shouldEnableThinking = modelSupportsThinking && agentEnablesThinking;

                if (shouldEnableThinking)
                {
                    int? thinkingBudget = AgentServiceHelper.GetThinkingBudgetParameter(agentExecutionSetting, _logger);
                    _logger.LogInformation("[ConfigureThinkingMode] ✅ 启用思考模式 - 模型支持: {modelSupports}, Agent启用: {agentEnables}, ThinkingBudget: {budget}",
                        modelSupportsThinking, agentEnablesThinking, thinkingBudget);
                    ConfigureThinkingMode(true, thinkingBudget);
                }
                else
                {
                    _logger.LogInformation("[ConfigureThinkingMode] ❌ 禁用思考模式 - 模型支持: {modelSupports}, Agent启用: {agentEnables}",
                        modelSupportsThinking, agentEnablesThinking);
                    ConfigureThinkingMode(false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ConfigureThinkingMode] 配置思考模式时发生错误，使用默认配置: {message}", ex.Message);
                ConfigureThinkingMode(false);
            }
        }

        /// <summary>
        /// 更新用户消息记录的Answer字段，将大模型输出保存到同一条记录中
        /// </summary>
        /// <param name="userInput">用户输入</param>
        /// <param name="assistantOutput">大模型输出</param>
        /// <param name="userGuid">用户GUID</param>
        /// <param name="chatGuid">会话GUID</param>
        private async Task UpdateUserMessageWithAnswerAsync(string userInput, string assistantOutput, string userGuid, string chatGuid)
        {
            try
            {
                if (string.IsNullOrEmpty(userGuid) || string.IsNullOrEmpty(chatGuid))
                {
                    _logger.LogWarning("[UpdateUserMessageWithAnswerAsync] 用户GUID或会话GUID为空，跳过更新");
                    return;
                }

                // 获取ChatMessageRepository
                var chatMessageRepository = _serviceProvider.GetService<ChatMessageRepostory>();
                if (chatMessageRepository == null)
                {
                    _logger.LogError("[UpdateUserMessageWithAnswerAsync] 无法获取ChatMessageRepostory服务");
                    return;
                }

                var chatGuidParsed = Guid.Parse(chatGuid);
                var batchGuid = _chatRunDto?.BatchGuid ?? Guid.Empty;

                // 查找最近的用户消息记录（按创建时间倒序，取第一条用户消息）
                var userMessage = await chatMessageRepository.GetFirstAsync(x =>
                    x.ChatGUID == chatGuidParsed &&
                    x.UserGUID == userGuid &&
                    x.Role == ChatRoleConstant.User &&
                    x.BatchGUID == batchGuid);

                if (userMessage != null)
                {
                    // 更新Answer字段
                    userMessage.Answer = assistantOutput;
                    userMessage.ModifiedTime = DateTime.Now;

                    await chatMessageRepository.UpdateAsync(userMessage);

                    _logger.LogInformation("[UpdateUserMessageWithAnswerAsync] 已更新用户消息记录的Answer字段，ChatMessageGUID: {messageGuid}, 用户: {userGuid}, 会话: {chatGuid}",
                        userMessage.ChatMessageGUID, userGuid, chatGuid);
                }
                else
                {
                    _logger.LogWarning("[UpdateUserMessageWithAnswerAsync] 未找到对应的用户消息记录，用户: {userGuid}, 会话: {chatGuid}, BatchGUID: {batchGuid}",
                        userGuid, chatGuid, batchGuid);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[UpdateUserMessageWithAnswerAsync] 更新用户消息Answer字段时发生错误: {message}", ex.Message);
            }
        }

    }
}

